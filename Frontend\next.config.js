/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    unoptimized: true,
    remotePatterns: [],
    domains: [],
  },
  // Configuración específica para Windows
  experimental: {
    esmExternals: false,
  },
  // Evitar problemas con enlaces simbólicos en Windows
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
      };
    }
    return config;
  },
};

module.exports = nextConfig;
